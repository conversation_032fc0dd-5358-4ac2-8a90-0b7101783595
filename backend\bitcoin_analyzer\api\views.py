import requests
import csv
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.http import JsonResponse
import os
import json
import openai
from django.core.cache import cache
from datetime import datetime, timedelta

class BitcoinPriceView(APIView):
    def get(self, request):
        try:
            price = self.fetch_bitcoin_price()
            return Response({"price": price}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def fetch_bitcoin_price(self):
        url = "https://api.livecoinwatch.com/coins/single"
        headers = {
            'content-type': 'application/json',
            'x-api-key': '2616e337-d7ea-4b39-a764-9c423c995298',
        }
        payload = {
            "currency": "USD",
            "code": "BTC",
            "meta": True
        }

        response = requests.post(url, json=payload, headers=headers)
        data = response.json()

        if response.status_code == 200:
            return data['rate']
        else:
            raise Exception(f"Error fetching data: {data}")

class ChatbotView(APIView):
    def post(self, request):
        try:
            query = request.data.get('query')
            if not query:
                return Response({"error": "Query is required"}, status=status.HTTP_400_BAD_REQUEST)

            # Try to use enhanced RAG system
            try:
                import sys
                import os

                # Add the BTC_Chatbot directory to the Python path
                # Go up from api/views.py -> api -> bitcoin_analyzer -> backend -> BTC_Chatbot
                current_dir = os.path.dirname(__file__)  # api directory
                project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
                backend_dir = os.path.dirname(project_dir)  # backend directory
                rag_path = os.path.join(backend_dir, 'BTC_Chatbot')

                if rag_path not in sys.path:
                    sys.path.append(rag_path)

                from Rag import generate_enhanced_response

                # Use enhanced RAG system (GPT-4o-mini + RAG supplementation)
                enhanced_result = generate_enhanced_response(query)

                return Response({
                    "response": enhanced_result["content"],
                    "model": enhanced_result["model"],
                    "rag_enhanced": enhanced_result["rag_enhanced"],
                    "sources_used": enhanced_result["sources_used"],
                    "relevance_scores": enhanced_result["relevance_scores"],
                    "context_available": enhanced_result["context_available"],
                    "type": "enhanced" if enhanced_result["rag_enhanced"] else "standard",
                    "source": "GPT-4o-mini with RAG Enhancement" if enhanced_result["rag_enhanced"] else "GPT-4o-mini"
                }, status=status.HTTP_200_OK)

            except Exception as rag_error:
                print(f"Enhanced RAG system error: {str(rag_error)}")

                # Fallback to basic OpenAI without RAG
                client = openai.OpenAI(api_key="********************************************************************************************************************************************************************")

                # Generate basic response
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are a Bitcoin expert answering queries with precise facts."},
                        {"role": "user", "content": query}
                    ]
                )

                return Response({
                    "response": response.choices[0].message.content,
                    "model": "gpt-4o-mini",
                    "rag_enhanced": False,
                    "sources_used": [],
                    "context_available": False,
                    "type": "fallback",
                    "source": "GPT-4o-mini (Fallback)",
                    "note": "Enhanced RAG system temporarily unavailable"
                }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FearGreedIndexView(APIView):
    # Define the cache key for the Fear & Greed Index data
    CACHE_KEY = 'fear_greed_index_data'
    CACHE_TIMEOUT = 60 * 60  # 1 hour in seconds

    def get(self, request):
        try:
            # Try to get data from cache first
            cached_data = cache.get(self.CACHE_KEY)
            if cached_data:
                print("Returning cached Fear & Greed Index data")
                return Response(cached_data, status=status.HTTP_200_OK)

            # If not in cache, fetch from API
            # Use Alternative.me Fear & Greed Index API which is free and doesn't require an API key
            url = 'https://api.alternative.me/fng/'

            # Add more detailed logging
            print(f"Fetching Fear & Greed Index from {url}")

            # Increase timeout and add headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=15)

            # Log response status
            print(f"Response status code: {response.status_code}")

            # Raise for HTTP errors
            response.raise_for_status()

            # Try to parse JSON
            try:
                data = response.json()
                print(f"Response data keys: {data.keys() if data else 'None'}")
            except ValueError as json_err:
                print(f"JSON parsing error: {str(json_err)}")
                print(f"Response content: {response.text[:200]}...")  # Print first 200 chars
                return Response({'error': f'Invalid JSON response: {str(json_err)}'},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            if not data or 'data' not in data:
                print(f"Missing 'data' key in response: {data}")
                return Response({'error': 'No Fear & Greed data returned'},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Format the response to include only the relevant data
            latest_data = data['data'][0] if data['data'] else {}
            print(f"Latest data: {latest_data}")

            formatted_data = {
                'value': latest_data.get('value'),
                'value_classification': latest_data.get('value_classification'),
                'timestamp': latest_data.get('timestamp'),
                'time_until_update': latest_data.get('time_until_update'),
                'source': 'Alternative.me'
            }

            # Cache the data
            cache.set(self.CACHE_KEY, formatted_data, self.CACHE_TIMEOUT)

            return Response(formatted_data, status=status.HTTP_200_OK)

        except requests.RequestException as e:
            print(f"Request exception: {str(e)}")
            return Response({'error': f"Fear & Greed API error: {str(e)}"},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            return Response({'error': f"Unexpected error: {str(e)}"},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class FearGreedIndexClearCacheView(APIView):
    def post(self, request):
        try:
            cache.delete(FearGreedIndexView.CACHE_KEY)
            return Response({"message": "Fear & Greed Index cache cleared successfully"},
                           status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": f"Failed to clear cache: {str(e)}"},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BinanceKlinesView(APIView):
    def get(self, request):
        interval = request.GET.get('interval', '1h')  # Default to 1h
        valid_intervals = ['1m', '5m', '15m', '1h', '4h', '1d']
        if interval not in valid_intervals:
            return JsonResponse({'error': f"Invalid interval. Use: {', '.join(valid_intervals)}"}, status=400)
        try:
            url = f"https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval={interval}&limit=1000"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            if not data or len(data) == 0:
                return JsonResponse({'error': 'No Kline data returned from Binance'}, status=500)
            return JsonResponse({'klines': data})
        except requests.RequestException as e:
            return JsonResponse({'error': f"Binance Kline API error: {str(e)}"}, status=500)


class BinanceOrderBookView(APIView):
    def get(self, request):
        symbol = request.GET.get('symbol', 'BTCUSDT').upper()
        limit = int(request.GET.get('limit', 10))

        # Validate limit
        valid_limits = [5, 10, 20, 50, 100, 500, 1000, 5000]
        if limit not in valid_limits:
            return JsonResponse({'error': f"Invalid limit. Use: {', '.join(map(str, valid_limits))}"}, status=400)

        try:
            url = f"https://api.binance.com/api/v3/depth"
            params = {"symbol": symbol, "limit": limit}
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if not data or 'bids' not in data or 'asks' not in data:
                return JsonResponse({'error': 'Invalid order book data returned from Binance'}, status=500)

            # Process bids and asks
            bids = []
            asks = []

            # Process bids (buyers) - sort by price descending
            cumulative_bid_qty = 0
            for bid in data['bids']:
                price = float(bid[0])
                quantity = float(bid[1])
                cumulative_bid_qty += quantity
                bids.append({
                    'price': price,
                    'quantity': quantity,
                    'cumulative': cumulative_bid_qty,
                    'type': 'bid'
                })

            # Process asks (sellers) - sort by price ascending
            cumulative_ask_qty = 0
            for ask in data['asks']:
                price = float(ask[0])
                quantity = float(ask[1])
                cumulative_ask_qty += quantity
                asks.append({
                    'price': price,
                    'quantity': quantity,
                    'cumulative': cumulative_ask_qty,
                    'type': 'ask'
                })

            # Calculate market metrics
            best_bid = bids[0]['price'] if bids else 0
            best_ask = asks[0]['price'] if asks else 0
            spread = best_ask - best_bid if best_bid and best_ask else 0
            total_bid_volume = sum(bid['quantity'] for bid in bids)
            total_ask_volume = sum(ask['quantity'] for ask in asks)

            return JsonResponse({
                'symbol': symbol,
                'bids': bids,
                'asks': asks,
                'metrics': {
                    'best_bid': best_bid,
                    'best_ask': best_ask,
                    'spread': spread,
                    'total_bid_volume': total_bid_volume,
                    'total_ask_volume': total_ask_volume
                },
                'timestamp': data.get('lastUpdateId', 0)
            })

        except requests.RequestException as e:
            return JsonResponse({'error': f"Binance Order Book API error: {str(e)}"}, status=500)


class BitcoinNewsView(APIView):
    CACHE_KEY = 'bitcoin_news_data'
    CACHE_TIMEOUT = 30 * 60  # 30 minutes

    def is_english_text(self, text):
        """Simple check to determine if text is likely in English"""
        if not text:
            return True  # Empty text is considered valid

        # Common English words that are good indicators
        english_indicators = [
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'must', 'can', 'bitcoin', 'cryptocurrency', 'market',
            'price', 'trading', 'investment', 'news', 'analysis'
        ]

        text_lower = text.lower()
        words = text_lower.split()

        if len(words) < 3:
            return True  # Very short text, assume it's fine

        # Count how many English indicator words are present
        english_word_count = sum(1 for word in words if any(indicator in word for indicator in english_indicators))

        # If at least 20% of words are English indicators, consider it English
        return english_word_count / len(words) >= 0.2

    def is_bitcoin_related(self, article):
        """Check if article is specifically about Bitcoin and in English"""
        title = article.get('title', '') or ''
        description = article.get('description', '') or ''
        keywords = article.get('keywords', []) or []

        # First check if the content appears to be in English
        if not (self.is_english_text(title) and self.is_english_text(description)):
            return False

        # Safely handle keywords that might be None
        if keywords:
            keywords = [k.lower() for k in keywords if k is not None]
        else:
            keywords = []

        # Bitcoin-related terms to look for
        bitcoin_terms = [
            'bitcoin', 'btc', 'bitcoin price', 'bitcoin market',
            'bitcoin trading', 'bitcoin value', 'cryptocurrency bitcoin',
            'bitcoin investment', 'bitcoin analysis', 'bitcoin news'
        ]

        # Check if any Bitcoin terms appear in title, description, or keywords
        text_to_check = f"{title.lower()} {description.lower()} {' '.join(keywords)}"
        return any(term in text_to_check for term in bitcoin_terms)

    def get(self, request):
        try:
            # Try to get data from cache first
            cached_data = cache.get(self.CACHE_KEY)
            if cached_data:
                print("Returning cached Bitcoin news data")
                return Response(cached_data, status=status.HTTP_200_OK)

            # NewsData.io API configuration
            API_KEY = 'pub_80dc42b28d5747bcba45082d7100a76f'
            BASE_URL = 'https://newsdata.io/api/1/news'

            # Define parameters for Bitcoin-specific news
            # Add language parameter to ensure English articles
            params = {
                'apikey': API_KEY,
                'q': 'Bitcoin',
                'language': 'en'
            }

            print(f"Fetching Bitcoin news from {BASE_URL}")

            # Add headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(BASE_URL, params=params, headers=headers, timeout=15)

            print(f"Response status code: {response.status_code}")
            print(f"Request URL: {response.url}")

            if response.status_code != 200:
                print(f"Error response content: {response.text[:500]}")

            response.raise_for_status()

            try:
                data = response.json()
                print(f"Response data keys: {data.keys() if data else 'None'}")
            except ValueError as json_err:
                print(f"JSON parsing error: {str(json_err)}")
                return Response({'error': f'Invalid JSON response: {str(json_err)}'},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            if not data or 'results' not in data:
                print(f"Missing 'results' key in response: {data}")
                return Response({'error': 'No news articles returned'},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            articles = data.get('results', [])
            if not articles:
                return Response({'error': 'No Bitcoin news articles found'},
                               status=status.HTTP_404_NOT_FOUND)

            # Format the articles for frontend consumption
            formatted_articles = []
            for article in articles:
                # Only include articles that are specifically about Bitcoin
                if self.is_bitcoin_related(article):
                    # Safely handle potentially None values
                    creator = article.get('creator', []) or []
                    keywords = article.get('keywords', []) or []

                    formatted_article = {
                        'title': article.get('title', 'No title available') or 'No title available',
                        'description': article.get('description', 'No description available') or 'No description available',
                        'pubDate': article.get('pubDate', '') or '',
                        'source_id': article.get('source_id', 'Unknown') or 'Unknown',
                        'link': article.get('link', '') or '',
                        'image_url': article.get('image_url', '') or '',
                        'creator': creator if isinstance(creator, list) else [],
                        'keywords': keywords if isinstance(keywords, list) else []
                    }
                    formatted_articles.append(formatted_article)

                    # Limit to 10 Bitcoin-specific articles
                    if len(formatted_articles) >= 10:
                        break

            # Check if we found any Bitcoin-specific articles
            if not formatted_articles:
                return Response({'error': 'No Bitcoin-specific news articles found'},
                               status=status.HTTP_404_NOT_FOUND)

            response_data = {
                'articles': formatted_articles,
                'total_results': len(formatted_articles),
                'source': 'NewsData.io',
                'filtered_for': 'Bitcoin-specific content in English'
            }

            # Cache the data
            cache.set(self.CACHE_KEY, response_data, self.CACHE_TIMEOUT)

            return Response(response_data, status=status.HTTP_200_OK)

        except requests.RequestException as e:
            print(f"NewsData.io API error: {str(e)}")
            print(f"Error type: {type(e)}")

            # For now, return mock data to test the frontend
            mock_articles = [
                {
                    'title': 'Bitcoin Reaches New Heights as Market Sentiment Improves',
                    'description': 'Bitcoin price surged today as institutional investors showed renewed interest in the cryptocurrency market.',
                    'pubDate': '2024-01-15T10:30:00Z',
                    'source_id': 'crypto-news',
                    'link': 'https://example.com/bitcoin-news-1',
                    'image_url': '',
                    'creator': ['Crypto Reporter'],
                    'keywords': ['bitcoin', 'cryptocurrency', 'market']
                },
                {
                    'title': 'Bitcoin Trading Volume Hits Record High',
                    'description': 'Daily Bitcoin trading volume reached unprecedented levels as more retail investors enter the market.',
                    'pubDate': '2024-01-15T08:15:00Z',
                    'source_id': 'financial-times',
                    'link': 'https://example.com/bitcoin-news-2',
                    'image_url': '',
                    'creator': ['Financial Analyst'],
                    'keywords': ['bitcoin', 'trading', 'volume']
                },
                {
                    'title': 'Major Corporation Adds Bitcoin to Treasury',
                    'description': 'A Fortune 500 company announced it has added Bitcoin to its corporate treasury as a hedge against inflation.',
                    'pubDate': '2024-01-14T16:45:00Z',
                    'source_id': 'business-wire',
                    'link': 'https://example.com/bitcoin-news-3',
                    'image_url': '',
                    'creator': ['Business Reporter'],
                    'keywords': ['bitcoin', 'corporate', 'treasury']
                }
            ]

            response_data = {
                'articles': mock_articles,
                'total_results': len(mock_articles),
                'source': 'Mock Data (API temporarily unavailable)',
                'filtered_for': 'Bitcoin-specific content in English',
                'note': 'This is sample data. The NewsData.io API is currently unavailable.'
            }

            return Response(response_data, status=status.HTTP_200_OK)
        except Exception as e:
            print(f"Unexpected error in BitcoinNewsView: {str(e)}")
            return Response({'error': f'Internal server error: {str(e)}'},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SentimentAnalysisView(APIView):
    CACHE_KEY = 'sentiment_analysis_data'
    CACHE_TIMEOUT = 60 * 60  # 1 hour in seconds

    def get_csv_path(self):
        """Get the path to the sentiment CSV file"""
        # Go up from api/views.py -> api -> bitcoin_analyzer -> backend -> Data_layer
        current_dir = os.path.dirname(__file__)  # api directory
        project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        csv_path = os.path.join(backend_dir, 'Data_layer', 'augmento_btc.csv')
        return csv_path

    def calculate_platform_sentiment(self, row, platform):
        """Get sentiment for a specific platform from aggregated columns"""
        column_name = f'{platform}_avg'

        try:
            # Get the pre-aggregated sentiment value
            sentiment_value = float(row[column_name])
            return min(max(sentiment_value, 0), 1)  # Clamp between 0 and 1
        except (ValueError, KeyError, TypeError):
            return 0.5  # Neutral if no data or invalid data

    def get_sentiment_classification(self, sentiment_score):
        """Classify sentiment based on score"""
        if sentiment_score > 0.6:
            return "Very Bullish"
        elif sentiment_score > 0.5:
            return "Bullish"
        elif sentiment_score > 0.4:
            return "Bearish"
        else:
            return "Very Bearish"

    def get(self, request):
        try:
            # Get query parameters
            platform = request.GET.get('platform', 'all')  # all, twitter, bitcointalk, reddit
            days = int(request.GET.get('days', 7))  # Default to 7 days for latest data

            # Try to get data from cache first
            cache_key = f"{self.CACHE_KEY}_{platform}_{days}"
            cached_data = cache.get(cache_key)
            if cached_data:
                print(f"Returning cached sentiment data for {platform}, {days} days")
                return Response(cached_data, status=status.HTTP_200_OK)

            csv_path = self.get_csv_path()

            if not os.path.exists(csv_path):
                return Response({'error': 'Sentiment data file not found'},
                              status=status.HTTP_404_NOT_FOUND)

            # Read and process CSV data using actual dates from CSV
            sentiment_data = []
            platforms = ['twitter', 'bitcointalk', 'reddit']

            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)

                # Read all data to find the latest dates
                all_data = []

                for row in reader:
                    try:
                        # Parse date - use actual CSV date
                        date_str = row['date']
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        timestamp = int(date_obj.timestamp() * 1000)

                        # Calculate sentiment for each platform
                        platform_sentiments = {}
                        overall_sentiment = 0

                        for plat in platforms:
                            sentiment = self.calculate_platform_sentiment(row, plat)
                            platform_sentiments[plat] = sentiment
                            overall_sentiment += sentiment

                        overall_sentiment = overall_sentiment / len(platforms)

                        # Get Bitcoin price from CSV
                        btc_price = float(row['listing_close'])

                        data_point = {
                            'date': date_str,
                            'timestamp': timestamp,
                            'btc_price': btc_price,
                            'overall_sentiment': overall_sentiment,
                            'sentiment_classification': self.get_sentiment_classification(overall_sentiment),
                            'platforms': platform_sentiments,
                            'date_obj': date_obj  # Keep for filtering
                        }

                        all_data.append(data_point)

                    except (ValueError, KeyError) as e:
                        continue  # Skip invalid rows

                # Sort by date to ensure chronological order
                all_data.sort(key=lambda x: x['date_obj'])

                # Filter by days from the LATEST date in the CSV (working backwards)
                if days > 0 and len(all_data) > 0:
                    # Find the latest date in the data
                    latest_date = all_data[-1]['date_obj']
                    cutoff_date = latest_date - timedelta(days=days)

                    # Filter data to include only the last N days from the latest date
                    sentiment_data = [
                        item for item in all_data
                        if item['date_obj'] >= cutoff_date
                    ]
                else:
                    sentiment_data = all_data

                # Remove the temporary date_obj field
                for item in sentiment_data:
                    del item['date_obj']

                # Sample data for better performance - take every nth item for longer periods
                if len(sentiment_data) > 500:
                    step = len(sentiment_data) // 500
                    sentiment_data = sentiment_data[::step]

                # Calculate summary statistics
                if sentiment_data:
                    recent_sentiment = sentiment_data[-1]['overall_sentiment']
                    avg_sentiment = sum(d['overall_sentiment'] for d in sentiment_data) / len(sentiment_data)

                    # Calculate correlation with price (simplified)
                    prices = [d['btc_price'] for d in sentiment_data]
                    sentiments = [d['overall_sentiment'] for d in sentiment_data]

                    # Simple correlation calculation
                    if len(prices) > 1:
                        price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
                        sentiment_changes = [sentiments[i] - sentiments[i-1] for i in range(1, len(sentiments))]

                        if price_changes and sentiment_changes:
                            correlation = sum(p * s for p, s in zip(price_changes, sentiment_changes)) / len(price_changes)
                        else:
                            correlation = 0
                    else:
                        correlation = 0
                else:
                    recent_sentiment = 0.5
                    avg_sentiment = 0.5
                    correlation = 0

                response_data = {
                    'data': sentiment_data,
                    'summary': {
                        'total_points': len(sentiment_data),
                        'recent_sentiment': recent_sentiment,
                        'recent_classification': self.get_sentiment_classification(recent_sentiment),
                        'average_sentiment': avg_sentiment,
                        'average_classification': self.get_sentiment_classification(avg_sentiment),
                        'price_correlation': correlation,
                        'platform_filter': platform,
                        'days_analyzed': days
                    },
                    'platforms_available': platforms
                }

                # Cache the data
                cache.set(cache_key, response_data, self.CACHE_TIMEOUT)

                return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error in SentimentAnalysisView: {str(e)}")
            return Response({'error': f'Internal server error: {str(e)}'},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BitcoinHashRateView(APIView):
    CACHE_KEY = 'bitcoin_hash_rate_data'
    CACHE_TIMEOUT = 60 * 60 * 24  # 24 hours in seconds

    def get_hash_rate_path(self):
        """Get the path to the Bitcoin hash rate JSON file"""
        current_dir = os.path.dirname(__file__)  # api directory
        project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        json_path = os.path.join(backend_dir, 'Data_layer', 'bitcoin_hash_rate.json')
        return json_path

    def get(self, request):
        try:
            # Get query parameters
            days = int(request.GET.get('days', 365))  # Default to 1 year

            # Try to get data from cache first
            cache_key = f"{self.CACHE_KEY}_{days}"
            cached_data = cache.get(cache_key)
            if cached_data:
                print(f"Returning cached hash rate data for {days} days")
                return Response(cached_data, status=status.HTTP_200_OK)

            # Read Bitcoin hash rate data
            hash_rate_path = self.get_hash_rate_path()
            if not os.path.exists(hash_rate_path):
                return Response({'error': 'Bitcoin hash rate data file not found'},
                              status=status.HTTP_404_NOT_FOUND)

            with open(hash_rate_path, 'r') as f:
                hash_rate_data = json.load(f)

            # Filter data based on days parameter
            all_data = hash_rate_data['hash_rate_data']

            if days < len(all_data):
                # Get the most recent data points
                filtered_data = all_data[-days:]
            else:
                filtered_data = all_data

            # Prepare response data
            response_data = {
                'metric': hash_rate_data['metric'],
                'description': hash_rate_data['description'],
                'unit': hash_rate_data['unit'],
                'granularity': hash_rate_data['granularity'],
                'total_data_points': len(all_data),
                'filtered_data_points': len(filtered_data),
                'date_range': {
                    'start': filtered_data[0]['date'] if filtered_data else None,
                    'end': filtered_data[-1]['date'] if filtered_data else None
                },
                'hash_rate_data': filtered_data,
                'summary': {
                    'latest_hash_rate': filtered_data[-1]['hash_rate'] if filtered_data else None,
                    'latest_date': filtered_data[-1]['date'] if filtered_data else None,
                    'earliest_hash_rate': filtered_data[0]['hash_rate'] if filtered_data else None,
                    'earliest_date': filtered_data[0]['date'] if filtered_data else None,
                    'analysis_period_days': days
                }
            }

            # Cache the data
            cache.set(cache_key, response_data, self.CACHE_TIMEOUT)

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error in BitcoinHashRateView: {str(e)}")
            return Response({'error': f'Failed to fetch hash rate data: {str(e)}'},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class HashRateCorrelationView(APIView):
    CACHE_KEY = 'hash_rate_correlation_data'
    CACHE_TIMEOUT = 60 * 60 * 24  # 24 hours in seconds

    def get_hash_rate_path(self):
        """Get the path to the Bitcoin hash rate JSON file"""
        current_dir = os.path.dirname(__file__)  # api directory
        project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        json_path = os.path.join(backend_dir, 'Data_layer', 'bitcoin_hash_rate.json')
        return json_path

    def get_bitcoin_price_data(self, days):
        """Get Bitcoin price data from CSV files"""
        try:
            # Use the same approach as CorrelationAnalysisView
            current_dir = os.path.dirname(__file__)
            project_dir = os.path.dirname(current_dir)
            backend_dir = os.path.dirname(project_dir)

            # Try to read from the daily CSV file first
            csv_path = os.path.join(backend_dir, 'Data_layer', 'Bitcoin Historical Data Daily.csv')

            if not os.path.exists(csv_path):
                print(f"Bitcoin daily CSV file not found at {csv_path}")
                return []

            bitcoin_data = []
            from datetime import datetime, timedelta
            import csv

            # Calculate cutoff date
            cutoff_date = datetime.now() - timedelta(days=days)

            with open(csv_path, 'r', encoding='utf-8-sig') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    try:
                        # Handle potential BOM in column names
                        date_key = 'Date'
                        price_key = 'Price'

                        # Find the correct keys (in case of BOM or other encoding issues)
                        for key in row.keys():
                            if 'Date' in key:
                                date_key = key
                            elif 'Price' in key:
                                price_key = key

                        # Skip empty rows
                        if not row[date_key] or not row[price_key]:
                            continue

                        # Parse date from M/D/YYYY format
                        date_str = row[date_key].strip()
                        date_obj = datetime.strptime(date_str, '%m/%d/%Y')

                        # Check if date is within our range
                        if date_obj >= cutoff_date:
                            # Parse price, removing commas and quotes
                            price_str = row[price_key].strip('"').replace(',', '')
                            price = float(price_str)

                            bitcoin_data.append({
                                'date': date_obj.strftime('%Y-%m-%d'),
                                'price': price,
                                'timestamp': int(date_obj.timestamp() * 1000)
                            })
                    except (ValueError, KeyError) as e:
                        print(f"Error parsing daily CSV row: {row}, Error: {e}")
                        continue

            # Sort by date (oldest first)
            bitcoin_data.sort(key=lambda x: x['date'])
            print(f"Loaded {len(bitcoin_data)} Bitcoin price data points from daily CSV")
            return bitcoin_data

        except Exception as e:
            print(f"Error reading Bitcoin price data: {str(e)}")
            return []

    def calculate_hash_rate_correlation(self, hash_rate_data, btc_data):
        """Calculate correlation between hash rate and Bitcoin prices"""
        try:
            # Align data by dates
            aligned_data = []
            btc_dict = {item['date']: item['price'] for item in btc_data}

            for hash_point in hash_rate_data:
                hash_date = hash_point['date']

                if hash_date in btc_dict:
                    aligned_data.append({
                        'date': hash_date,
                        'hash_rate': hash_point['hash_rate'],
                        'btc_price': btc_dict[hash_date],
                        'timestamp': hash_point['timestamp']
                    })

            if len(aligned_data) < 10:  # Need at least 10 data points
                return None, aligned_data

            # Calculate correlation using simple method to avoid complex numbers
            hash_rates = [float(point['hash_rate']) for point in aligned_data]
            btc_prices = [float(point['btc_price']) for point in aligned_data]

            # Try scipy first, fallback to simple calculation
            try:
                from scipy.stats import pearsonr
                correlation, p_value = pearsonr(hash_rates, btc_prices)

                # Ensure values are JSON serializable (not complex numbers)
                correlation = float(correlation.real) if hasattr(correlation, 'real') else float(correlation)
                p_value = float(p_value.real) if hasattr(p_value, 'real') else float(p_value)

                return {
                    'correlation': correlation,
                    'p_value': p_value,
                    'data_points': len(aligned_data),
                    'significance': 'significant' if p_value < 0.05 else 'not significant'
                }, aligned_data

            except ImportError:
                # Fallback to simple correlation
                return self.simple_correlation_calculation(hash_rates, btc_prices, aligned_data)

        except Exception as e:
            print(f"Error calculating hash rate correlation: {str(e)}")
            return None, []

    def simple_correlation_calculation(self, hash_rates, btc_prices, aligned_data):
        """Simple correlation calculation without scipy"""
        try:
            n = len(hash_rates)
            if n < 2:
                return None, aligned_data

            # Calculate means
            mean_hash = sum(hash_rates) / n
            mean_price = sum(btc_prices) / n

            # Calculate correlation coefficient
            numerator = sum((hash_rates[i] - mean_hash) * (btc_prices[i] - mean_price) for i in range(n))

            sum_sq_hash = sum((hash_rates[i] - mean_hash) ** 2 for i in range(n))
            sum_sq_price = sum((btc_prices[i] - mean_price) ** 2 for i in range(n))

            denominator = (sum_sq_hash * sum_sq_price) ** 0.5

            if denominator == 0:
                correlation = 0
            else:
                correlation = numerator / denominator

            return {
                'correlation': float(correlation),
                'p_value': None,  # Cannot calculate p-value without scipy
                'data_points': n,
                'significance': 'unknown'
            }, aligned_data

        except Exception as e:
            print(f"Error in simple correlation calculation: {str(e)}")
            return None, aligned_data

    def get(self, request):
        try:
            # Get query parameters
            days = int(request.GET.get('days', 365))  # Default to 1 year

            # Try to get data from cache first
            cache_key = f"{self.CACHE_KEY}_{days}"
            cached_data = cache.get(cache_key)
            if cached_data:
                print(f"Returning cached hash rate correlation data for {days} days")
                return Response(cached_data, status=status.HTTP_200_OK)

            # Read Bitcoin hash rate data
            hash_rate_path = self.get_hash_rate_path()
            if not os.path.exists(hash_rate_path):
                return Response({'error': 'Bitcoin hash rate data file not found'},
                              status=status.HTTP_404_NOT_FOUND)

            with open(hash_rate_path, 'r') as f:
                hash_rate_data = json.load(f)

            # Filter hash rate data by days
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=days)

            filtered_hash_data = []
            for point in hash_rate_data['hash_rate_data']:
                point_date = datetime.strptime(point['date'], '%Y-%m-%d')
                if point_date >= cutoff_date:
                    filtered_hash_data.append(point)

            # Get Bitcoin price data
            btc_data = self.get_bitcoin_price_data(days)

            # Calculate correlation
            correlation_stats, aligned_data = self.calculate_hash_rate_correlation(filtered_hash_data, btc_data)

            # Prepare response data
            response_data = {
                'correlation_analysis': correlation_stats,
                'hash_rate_data': filtered_hash_data,
                'bitcoin_data': btc_data,
                'aligned_data': aligned_data,
                'summary': {
                    'total_hash_points': len(filtered_hash_data),
                    'total_btc_points': len(btc_data),
                    'aligned_points': len(aligned_data),
                    'analysis_period_days': days,
                    'latest_hash_rate': filtered_hash_data[-1]['hash_rate'] if filtered_hash_data else None,
                    'latest_btc_price': btc_data[-1]['price'] if btc_data else None
                }
            }

            # Cache the data
            cache.set(cache_key, response_data, self.CACHE_TIMEOUT)

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error in HashRateCorrelationView: {str(e)}")
            return Response({'error': f'Failed to fetch hash rate correlation data: {str(e)}'},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class M2CorrelationView(APIView):
    CACHE_KEY = 'm2_correlation_data'
    CACHE_TIMEOUT = 60 * 60 * 24  # 24 hours in seconds

    def get_m2_path(self):
        """Get the path to the WM2NS CSV file"""
        current_dir = os.path.dirname(__file__)  # api directory
        project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        csv_path = os.path.join(backend_dir, 'Data_layer', 'WM2NS.csv')
        return csv_path

    def get_bitcoin_data_path(self):
        """Get the path to the Bitcoin Historical Data CSV file"""
        current_dir = os.path.dirname(__file__)  # api directory
        project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        csv_path = os.path.join(backend_dir, 'Data_layer', 'Bitcoin Historical Data.csv')
        return csv_path

    def read_m2_data(self):
        """Read M2 money supply data from WM2NS.csv"""
        try:
            m2_path = self.get_m2_path()
            if not os.path.exists(m2_path):
                print(f"M2 data file not found at: {m2_path}")
                return []

            m2_data = []
            with open(m2_path, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        # Parse date and M2 value
                        date_str = row['observation_date']
                        m2_value = float(row['WM2NS'])

                        # Convert date to YYYY-MM-DD format
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                        m2_data.append({
                            'date': date_str,
                            'value': m2_value
                        })
                    except (ValueError, KeyError) as e:
                        print(f"Error parsing M2 data row: {row}, error: {e}")
                        continue

            print(f"Successfully read {len(m2_data)} M2 data points")
            return m2_data

        except Exception as e:
            print(f"Error reading M2 data: {str(e)}")
            return []

    def read_bitcoin_data(self, days=None):
        """Read Bitcoin price data from local CSV and CoinGecko API"""
        try:
            btc_data = []

            # Read historical data from local CSV (before June 2024)
            btc_path = self.get_bitcoin_data_path()
            if os.path.exists(btc_path):
                with open(btc_path, 'r') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        try:
                            date_str = row['Date']
                            # Handle price with commas and quotes
                            price_str = row.get('Price', row.get('Close', '0'))
                            price_str = price_str.replace(',', '').replace('"', '')
                            price = float(price_str)

                            # Convert date to YYYY-MM-DD format
                            try:
                                # Try MM/DD/YYYY format first
                                date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                            except ValueError:
                                try:
                                    # Try YYYY-MM-DD format
                                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                                except ValueError:
                                    # Skip if date format is not recognized
                                    continue

                            formatted_date = date_obj.strftime('%Y-%m-%d')

                            btc_data.append({
                                'date': formatted_date,
                                'price': price
                            })
                        except (ValueError, KeyError) as e:
                            continue

            # Sort by date
            btc_data.sort(key=lambda x: x['date'])

            # Filter by days if specified
            if days and btc_data:
                cutoff_date = datetime.now() - timedelta(days=days)
                btc_data = [point for point in btc_data
                           if datetime.strptime(point['date'], '%Y-%m-%d') >= cutoff_date]

            print(f"Successfully read {len(btc_data)} Bitcoin data points")
            return btc_data

        except Exception as e:
            print(f"Error reading Bitcoin data: {str(e)}")
            return self.generate_fallback_btc_data(days)

    def generate_fallback_btc_data(self, days=None):
        """Generate fallback Bitcoin data if file reading fails"""
        try:
            # Generate some sample data for the last year
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days if days else 365)

            btc_data = []
            current_date = start_date
            base_price = 30000

            while current_date <= end_date:
                # Add some random variation
                import random
                price_variation = random.uniform(-0.05, 0.05)
                price = base_price * (1 + price_variation)
                base_price = price  # Use previous price as base for next

                btc_data.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'price': round(price, 2)
                })
                current_date += timedelta(days=1)

            return btc_data

        except Exception as e:
            print(f"Error generating fallback data: {str(e)}")
            return []

    def calculate_correlation(self, m2_data, btc_data):
        """Calculate correlation between M2 money supply and Bitcoin prices"""
        try:
            # Align data by dates
            aligned_data = []
            m2_dict = {item['date']: item['value'] for item in m2_data}

            for btc_point in btc_data:
                btc_date = btc_point['date']

                # For weekly M2 data, find the closest M2 data point
                if btc_date in m2_dict:
                    aligned_data.append({
                        'date': btc_date,
                        'm2_value': m2_dict[btc_date],
                        'btc_price': btc_point['price']
                    })
                else:
                    # Find closest M2 data point within a week
                    btc_date_obj = datetime.strptime(btc_date, '%Y-%m-%d')
                    closest_m2 = None
                    min_diff = float('inf')

                    for m2_date, m2_value in m2_dict.items():
                        m2_date_obj = datetime.strptime(m2_date, '%Y-%m-%d')
                        diff = abs((btc_date_obj - m2_date_obj).days)
                        if diff <= 7 and diff < min_diff:  # Within a week
                            min_diff = diff
                            closest_m2 = m2_value

                    if closest_m2 is not None:
                        aligned_data.append({
                            'date': btc_date,
                            'm2_value': closest_m2,
                            'btc_price': btc_point['price']
                        })

            if len(aligned_data) < 10:  # Need at least 10 data points
                return None, aligned_data

            # Calculate correlation using simple method to avoid complex numbers
            m2_values = [float(point['m2_value']) for point in aligned_data]
            btc_prices = [float(point['btc_price']) for point in aligned_data]

            # Try scipy first, fallback to simple calculation
            try:
                from scipy.stats import pearsonr
                correlation, p_value = pearsonr(m2_values, btc_prices)

                # Ensure values are JSON serializable (not complex numbers)
                correlation = float(correlation.real) if hasattr(correlation, 'real') else float(correlation)
                p_value = float(p_value.real) if hasattr(p_value, 'real') else float(p_value)

                return {
                    'correlation': correlation,
                    'p_value': p_value,
                    'data_points': len(aligned_data),
                    'significance': 'significant' if p_value < 0.05 else 'not significant'
                }, aligned_data

            except ImportError:
                # Fallback to simple correlation calculation
                return self.simple_correlation(m2_values, btc_prices), aligned_data

        except Exception as e:
            print(f"Error calculating correlation: {str(e)}")
            return None, []

    def simple_correlation(self, x, y):
        """Simple correlation calculation without scipy"""
        try:
            n = len(x)
            if n < 2:
                return None

            # Calculate means
            mean_x = sum(x) / n
            mean_y = sum(y) / n

            # Calculate correlation coefficient
            numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
            sum_sq_x = sum((x[i] - mean_x) ** 2 for i in range(n))
            sum_sq_y = sum((y[i] - mean_y) ** 2 for i in range(n))

            if sum_sq_x == 0 or sum_sq_y == 0:
                return None

            correlation = numerator / (sum_sq_x * sum_sq_y) ** 0.5

            return {
                'correlation': correlation,
                'p_value': None,  # Can't calculate p-value without scipy
                'data_points': n,
                'significance': 'unknown'
            }

        except Exception as e:
            print(f"Error in simple correlation: {str(e)}")
            return None

    def get(self, request):
        try:
            # Get query parameters
            days = int(request.GET.get('days', 365))  # Default to 1 year

            # Try to get data from cache first
            cache_key = f"{self.CACHE_KEY}_{days}"
            cached_data = cache.get(cache_key)
            if cached_data:
                print(f"Returning cached M2 correlation data for {days} days")
                return Response(cached_data, status=status.HTTP_200_OK)

            # Read M2 money supply data
            m2_data = self.read_m2_data()
            if not m2_data:
                return Response({'error': 'M2 money supply data not available'},
                              status=status.HTTP_404_NOT_FOUND)

            # Read Bitcoin price data
            btc_data = self.read_bitcoin_data(days)
            if not btc_data:
                return Response({'error': 'Bitcoin price data not available'},
                              status=status.HTTP_404_NOT_FOUND)

            # Calculate correlation
            correlation_stats, aligned_data = self.calculate_correlation(m2_data, btc_data)

            # Prepare response data
            response_data = {
                'correlation_analysis': correlation_stats,
                'm2_data': m2_data[-days:] if days < len(m2_data) else m2_data,
                'bitcoin_data': btc_data,
                'aligned_data': aligned_data,
                'summary': {
                    'total_m2_points': len(m2_data),
                    'total_btc_points': len(btc_data),
                    'aligned_points': len(aligned_data),
                    'analysis_period_days': days,
                    'latest_m2_value': m2_data[-1]['value'] if m2_data else None,
                    'latest_btc_price': btc_data[-1]['price'] if btc_data else None
                }
            }

            # Cache the data
            cache.set(cache_key, response_data, self.CACHE_TIMEOUT)

            return Response(response_data, status=status.HTTP_200_OK)

        except ValueError:
            return Response({'error': 'Invalid days parameter'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print(f"Error in M2CorrelationView: {str(e)}")
            return Response({'error': 'Internal server error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CorrelationAnalysisView(APIView):
    CACHE_KEY = 'correlation_analysis_data'
    CACHE_TIMEOUT = 60 * 60 * 2  # 2 hours in seconds

    def get_fedfunds_path(self):
        """Get the path to the FEDFUNDS CSV file"""
        current_dir = os.path.dirname(__file__)  # api directory
        project_dir = os.path.dirname(current_dir)  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        csv_path = os.path.join(backend_dir, 'Data_layer', 'FEDFUNDS.csv')
        return csv_path

    def fetch_bitcoin_historical_data(self, days=365):
        """Fetch historical Bitcoin price data from local CSV files"""
        try:
            # Calculate the cutoff date (June 1, 2024)
            cutoff_date = datetime(2024, 6, 1)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            bitcoin_data = []

            # If we need data before June 2024, read from historical CSV
            if start_date < cutoff_date:
                print(f"Fetching historical data from local CSV for dates before {cutoff_date.strftime('%Y-%m-%d')}")
                local_data = self.fetch_bitcoin_data_from_csv(start_date, min(cutoff_date, end_date))
                bitcoin_data.extend(local_data)

            # If we need data after June 2024, read from daily CSV
            if end_date > cutoff_date:
                daily_start_date = max(start_date, cutoff_date)
                print(f"Fetching recent data from daily CSV for dates after {cutoff_date.strftime('%Y-%m-%d')}")
                daily_data = self.fetch_bitcoin_data_from_daily_csv(daily_start_date, end_date)
                bitcoin_data.extend(daily_data)

            # Sort by date to ensure chronological order
            bitcoin_data.sort(key=lambda x: x['date'])

            # Remove duplicates if any (keep the most recent entry for each date)
            seen_dates = set()
            unique_data = []
            for item in reversed(bitcoin_data):  # Reverse to keep latest entries
                if item['date'] not in seen_dates:
                    seen_dates.add(item['date'])
                    unique_data.append(item)

            bitcoin_data = list(reversed(unique_data))  # Restore chronological order

            print(f"Total Bitcoin data points retrieved: {len(bitcoin_data)}")
            return bitcoin_data

        except Exception as e:
            print(f"Error fetching Bitcoin historical data: {str(e)}")
            return self.generate_fallback_bitcoin_data(min(days, 365))

    def fetch_bitcoin_data_from_csv(self, start_date, end_date):
        """Fetch Bitcoin historical data from local CSV file"""
        try:
            # Get the path to the Bitcoin Historical Data.csv file
            csv_path = self.get_bitcoin_csv_path()

            if not os.path.exists(csv_path):
                print(f"Bitcoin CSV file not found at {csv_path}")
                return []

            bitcoin_data = []

            with open(csv_path, 'r', encoding='utf-8-sig') as file:  # utf-8-sig handles BOM
                reader = csv.DictReader(file)
                for row in reader:
                    try:
                        # Handle potential BOM in column names
                        date_key = 'Date'
                        price_key = 'Price'

                        # Find the correct keys (in case of BOM or other encoding issues)
                        for key in row.keys():
                            if 'Date' in key:
                                date_key = key
                            elif 'Price' in key:
                                price_key = key

                        # Parse date from MM/DD/YYYY format
                        date_str = row[date_key].strip('"')
                        date_obj = datetime.strptime(date_str, '%m/%d/%Y')

                        # Check if date is within our range
                        if start_date <= date_obj <= end_date:
                            # Parse price, removing commas and quotes
                            price_str = row[price_key].strip('"').replace(',', '')
                            price = float(price_str)

                            bitcoin_data.append({
                                'date': date_obj.strftime('%Y-%m-%d'),
                                'price': price,
                                'timestamp': int(date_obj.timestamp() * 1000)  # Convert to milliseconds
                            })
                    except (ValueError, KeyError) as e:
                        print(f"Error parsing CSV row: {row}, Error: {e}")
                        continue

            # Sort by date (oldest first)
            bitcoin_data.sort(key=lambda x: x['date'])
            print(f"Loaded {len(bitcoin_data)} data points from CSV")
            return bitcoin_data

        except Exception as e:
            print(f"Error reading Bitcoin CSV file: {str(e)}")
            return []

    def get_bitcoin_csv_path(self):
        """Get the path to the Bitcoin Historical Data.csv file"""
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        csv_path = os.path.join(backend_dir, 'Data_layer', 'Bitcoin Historical Data.csv')
        return csv_path

    def get_bitcoin_daily_csv_path(self):
        """Get the path to the Bitcoin Historical Data Daily.csv file"""
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # bitcoin_analyzer directory
        backend_dir = os.path.dirname(project_dir)  # backend directory
        csv_path = os.path.join(backend_dir, 'Data_layer', 'Bitcoin Historical Data Daily.csv')
        return csv_path

    def fetch_bitcoin_data_from_daily_csv(self, start_date, end_date):
        """Fetch Bitcoin historical data from daily CSV file"""
        try:
            # Get the path to the Bitcoin Historical Data Daily.csv file
            csv_path = self.get_bitcoin_daily_csv_path()

            if not os.path.exists(csv_path):
                print(f"Bitcoin daily CSV file not found at {csv_path}")
                return []

            bitcoin_data = []

            with open(csv_path, 'r', encoding='utf-8-sig') as file:  # utf-8-sig handles BOM
                reader = csv.DictReader(file)
                for row in reader:
                    try:
                        # Handle potential BOM in column names
                        date_key = 'Date'
                        price_key = 'Price'

                        # Find the correct keys (in case of BOM or other encoding issues)
                        for key in row.keys():
                            if 'Date' in key:
                                date_key = key
                            elif 'Price' in key:
                                price_key = key

                        # Parse date from M/D/YYYY format (no quotes in daily CSV)
                        date_str = row[date_key].strip()
                        date_obj = datetime.strptime(date_str, '%m/%d/%Y')

                        # Check if date is within our range
                        if start_date <= date_obj <= end_date:
                            # Parse price, removing commas and quotes (daily CSV has quotes around prices)
                            price_str = row[price_key].strip('"').replace(',', '')
                            price = float(price_str)

                            bitcoin_data.append({
                                'date': date_obj.strftime('%Y-%m-%d'),
                                'price': price,
                                'timestamp': int(date_obj.timestamp() * 1000)  # Convert to milliseconds
                            })
                    except (ValueError, KeyError) as e:
                        print(f"Error parsing daily CSV row: {row}, Error: {e}")
                        continue

            # Sort by date (oldest first)
            bitcoin_data.sort(key=lambda x: x['date'])
            print(f"Loaded {len(bitcoin_data)} data points from daily CSV")
            return bitcoin_data

        except Exception as e:
            print(f"Error reading Bitcoin daily CSV file: {str(e)}")
            return []

    def fetch_bitcoin_data_from_api(self, days):
        """Fetch recent Bitcoin price data from CoinGecko API"""
        try:
            # Limit days to avoid rate limiting on free tier
            max_days = min(days, 365)  # CoinGecko free tier limit

            # CoinGecko API for historical data (free, no API key required)
            url = f"https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': max_days,
                'interval': 'daily'
            }

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json'
            }

            response = requests.get(url, params=params, headers=headers, timeout=15)

            # Check for rate limiting or auth issues
            if response.status_code == 401:
                print(f"CoinGecko API unauthorized - using fallback data for {max_days} days")
                return self.generate_fallback_bitcoin_data(max_days)
            elif response.status_code == 429:
                print(f"CoinGecko API rate limited - using fallback data for {max_days} days")
                return self.generate_fallback_bitcoin_data(max_days)

            response.raise_for_status()
            data = response.json()

            # Format the data
            bitcoin_data = []
            if 'prices' in data:
                for price_point in data['prices']:
                    timestamp = price_point[0]
                    price = price_point[1]
                    date = datetime.fromtimestamp(timestamp / 1000)
                    bitcoin_data.append({
                        'date': date.strftime('%Y-%m-%d'),
                        'price': price,
                        'timestamp': timestamp
                    })

            print(f"Loaded {len(bitcoin_data)} data points from CoinGecko API")
            return bitcoin_data

        except Exception as e:
            print(f"Error fetching Bitcoin data from API: {str(e)}")
            return self.generate_fallback_bitcoin_data(min(days, 365))

    def generate_fallback_bitcoin_data(self, days):
        """Generate fallback Bitcoin price data when API is unavailable"""
        try:
            bitcoin_data = []
            # Start from today and go backwards
            from datetime import date, timedelta

            # Use a base price and add some realistic variation
            base_price = 45000  # Approximate Bitcoin price
            current_date = date.today()

            for i in range(days):
                # Generate realistic price variation (±5% daily)
                import random
                variation = random.uniform(-0.05, 0.05)
                price = base_price * (1 + variation)

                # Add some trend (slight upward bias)
                trend_factor = 1 + (i * 0.0001)  # Very small daily trend
                price *= trend_factor

                date_obj = current_date - timedelta(days=i)
                # Convert date to timestamp (works on all platforms)
                datetime_obj = datetime.combine(date_obj, datetime.min.time())
                timestamp = int(datetime_obj.timestamp()) * 1000

                bitcoin_data.append({
                    'date': date_obj.strftime('%Y-%m-%d'),
                    'price': round(price, 2),
                    'timestamp': timestamp
                })

            # Reverse to get chronological order
            bitcoin_data.reverse()
            return bitcoin_data

        except Exception as e:
            print(f"Error generating fallback data: {str(e)}")
            return []

    def calculate_correlation(self, fed_data, btc_data):
        """Calculate correlation between Fed rates and Bitcoin prices"""
        try:
            # Align data by dates
            aligned_data = []
            fed_dict = {item['date']: item['rate'] for item in fed_data}

            for btc_point in btc_data:
                btc_date = btc_point['date']
                # For monthly Fed data, match to the first day of the month
                month_key = btc_date[:7] + '-01'  # YYYY-MM-01 format

                if month_key in fed_dict:
                    aligned_data.append({
                        'date': btc_date,
                        'fed_rate': fed_dict[month_key],
                        'btc_price': btc_point['price']
                    })

            if len(aligned_data) < 10:  # Need at least 10 data points
                return None, aligned_data

            # Calculate correlation using simple method to avoid complex numbers
            fed_rates = [float(point['fed_rate']) for point in aligned_data]
            btc_prices = [float(point['btc_price']) for point in aligned_data]

            # Try scipy first, fallback to simple calculation
            try:
                from scipy.stats import pearsonr
                correlation, p_value = pearsonr(fed_rates, btc_prices)

                # Ensure values are JSON serializable (not complex numbers)
                correlation = float(correlation.real) if hasattr(correlation, 'real') else float(correlation)
                p_value = float(p_value.real) if hasattr(p_value, 'real') else float(p_value)

                return {
                    'correlation': correlation,
                    'p_value': p_value,
                    'data_points': len(aligned_data),
                    'significance': 'significant' if p_value < 0.05 else 'not significant'
                }, aligned_data

            except ImportError:
                # Fallback to simple correlation
                return self.simple_correlation_aligned(fed_rates, btc_prices, aligned_data)

        except Exception as e:
            print(f"Error calculating correlation: {str(e)}")
            return self.simple_correlation(fed_data, btc_data)

    def simple_correlation_aligned(self, fed_rates, btc_prices, aligned_data):
        """Simple correlation calculation for already aligned data"""
        try:
            n = len(fed_rates)
            if n < 2:
                return None, aligned_data

            # Simple Pearson correlation
            sum_fed = sum(fed_rates)
            sum_btc = sum(btc_prices)
            sum_fed_sq = sum(x * x for x in fed_rates)
            sum_btc_sq = sum(x * x for x in btc_prices)
            sum_fed_btc = sum(fed_rates[i] * btc_prices[i] for i in range(n))

            numerator = n * sum_fed_btc - sum_fed * sum_btc
            denominator_sq = (n * sum_fed_sq - sum_fed * sum_fed) * (n * sum_btc_sq - sum_btc * sum_btc)

            if denominator_sq <= 0:
                correlation = 0.0
            else:
                correlation = numerator / (denominator_sq ** 0.5)

            # Ensure correlation is a real number and JSON serializable
            correlation = float(correlation) if not (hasattr(correlation, 'imag') and correlation.imag != 0) else 0.0

            return {
                'correlation': correlation,
                'p_value': None,
                'data_points': n,
                'significance': 'calculated'
            }, aligned_data

        except Exception as e:
            print(f"Error in simple correlation aligned: {str(e)}")
            return None, aligned_data

    def simple_correlation(self, fed_data, btc_data):
        """Simple correlation calculation without scipy"""
        try:
            # Align data by dates
            aligned_data = []
            fed_dict = {item['date']: item['rate'] for item in fed_data}

            for btc_point in btc_data:
                btc_date = btc_point['date']
                month_key = btc_date[:7] + '-01'

                if month_key in fed_dict:
                    aligned_data.append({
                        'date': btc_date,
                        'fed_rate': float(fed_dict[month_key]),
                        'btc_price': float(btc_point['price'])
                    })

            if len(aligned_data) < 10:
                return None, aligned_data

            # Extract aligned values
            fed_rates = [point['fed_rate'] for point in aligned_data]
            btc_prices = [point['btc_price'] for point in aligned_data]

            return self.simple_correlation_aligned(fed_rates, btc_prices, aligned_data)

        except Exception as e:
            print(f"Error in simple correlation: {str(e)}")
            return None, []

    def get(self, request):
        try:
            # Get query parameters
            days = int(request.GET.get('days', 365))  # Default to 1 year

            # Try to get data from cache first
            cache_key = f"{self.CACHE_KEY}_{days}"
            cached_data = cache.get(cache_key)
            if cached_data:
                print(f"Returning cached correlation analysis data for {days} days")
                return Response(cached_data, status=status.HTTP_200_OK)

            # Read Federal Funds Rate data
            fedfunds_path = self.get_fedfunds_path()
            if not os.path.exists(fedfunds_path):
                return Response({'error': 'Federal Funds Rate data file not found'},
                              status=status.HTTP_404_NOT_FOUND)

            fed_data = []
            with open(fedfunds_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    try:
                        fed_data.append({
                            'date': row['observation_date'],
                            'rate': float(row['FEDFUNDS'])
                        })
                    except (ValueError, KeyError):
                        continue

            # Fetch Bitcoin historical data
            btc_data = self.fetch_bitcoin_historical_data(days)

            if not btc_data:
                return Response({'error': 'Failed to fetch Bitcoin historical data'},
                              status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Calculate correlation
            correlation_stats, aligned_data = self.calculate_correlation(fed_data, btc_data)

            # Prepare response data
            response_data = {
                'correlation_analysis': correlation_stats,
                'fed_funds_data': fed_data[-days:] if days < len(fed_data) else fed_data,
                'bitcoin_data': btc_data,
                'aligned_data': aligned_data,
                'summary': {
                    'total_fed_points': len(fed_data),
                    'total_btc_points': len(btc_data),
                    'aligned_points': len(aligned_data),
                    'analysis_period_days': days,
                    'latest_fed_rate': fed_data[-1]['rate'] if fed_data else None,
                    'latest_btc_price': btc_data[-1]['price'] if btc_data else None
                }
            }

            # Cache the data
            cache.set(cache_key, response_data, self.CACHE_TIMEOUT)

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error in CorrelationAnalysisView: {str(e)}")
            return Response({'error': f'Internal server error: {str(e)}'},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
