import React, { useState, useEffect } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

interface M2CorrelationData {
  correlation_analysis: {
    correlation: number;
    p_value?: number;
    data_points: number;
    significance: string;
  } | null;
  m2_data: Array<{
    date: string;
    value: number;
  }>;
  bitcoin_data: Array<{
    date: string;
    price: number;
  }>;
  aligned_data: Array<{
    date: string;
    m2_value: number;
    btc_price: number;
  }>;
  summary: {
    total_m2_points: number;
    total_btc_points: number;
    aligned_points: number;
    analysis_period_days: number;
    latest_m2_value: number | null;
    latest_btc_price: number | null;
  };
}

const M2CorrelationChart: React.FC = () => {
  const [correlationData, setCorrelationData] = useState<M2CorrelationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(365);

  const periodOptions = [
    { value: 90, label: '3 Months' },
    { value: 180, label: '6 Months' },
    { value: 365, label: '1 Year' },
    { value: 730, label: '2 Years' },
    { value: 1095, label: '3 Years' },
    { value: 1825, label: '5 Years' },
  ];

  const fetchCorrelationData = async (days: number) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/m2-correlation/?days=${days}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setCorrelationData(data);
    } catch (err) {
      console.error('Error fetching M2 correlation data:', err);
      setError('Failed to load M2 correlation data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCorrelationData(selectedPeriod);
  }, [selectedPeriod]);

  const getCorrelationColor = (correlation: number | null): string => {
    if (correlation === null) return '#6B7280';
    if (correlation > 0.7) return '#10B981'; // Strong positive - green
    if (correlation > 0.3) return '#F59E0B'; // Moderate positive - amber
    if (correlation > -0.3) return '#6B7280'; // Weak - gray
    if (correlation > -0.7) return '#F97316'; // Moderate negative - orange
    return '#EF4444'; // Strong negative - red
  };

  const getCorrelationDescription = (correlation: number | null): string => {
    if (correlation === null) return 'No correlation data';
    if (correlation > 0.7) return 'Strong Positive';
    if (correlation > 0.3) return 'Moderate Positive';
    if (correlation > -0.3) return 'Weak';
    if (correlation > -0.7) return 'Moderate Negative';
    return 'Strong Negative';
  };

  // Prepare chart data
  const chartSeries = correlationData ? [
    {
      name: 'Bitcoin Price',
      type: 'line',
      yAxisIndex: 0,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.btc_price
      }))
    },
    {
      name: 'M2 Money Supply',
      type: 'line',
      yAxisIndex: 1,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.m2_value
      }))
    }
  ] : [];

  const chartOptions: ApexOptions = {
    chart: {
      type: 'line',
      height: 400,
      zoom: {
        enabled: true
      },
      toolbar: {
        show: true
      }
    },
    colors: ['#F59E0B', '#8B5CF6'],
    stroke: {
      width: [2, 2],
      curve: 'smooth'
    },
    xaxis: {
      type: 'datetime',
      labels: {
        style: {
          colors: '#6B7280'
        }
      }
    },
    yaxis: [
      {
        title: {
          text: 'Bitcoin Price (USD)',
          style: {
            color: '#F59E0B'
          }
        },
        labels: {
          style: {
            colors: '#F59E0B'
          },
          formatter: (value) => `$${value.toLocaleString()}`
        }
      },
      {
        opposite: true,
        title: {
          text: 'M2 Money Supply (Billions USD)',
          style: {
            color: '#8B5CF6'
          }
        },
        labels: {
          style: {
            colors: '#8B5CF6'
          },
          formatter: (value) => `$${(value / 1000).toFixed(1)}T`
        }
      }
    ],
    legend: {
      position: 'top',
      horizontalAlign: 'left'
    },
    grid: {
      borderColor: '#E5E7EB'
    },
    tooltip: {
      shared: true,
      intersect: false,
      x: {
        format: 'dd MMM yyyy'
      },
      y: [
        {
          formatter: (value) => `$${value.toLocaleString()}`
        },
        {
          formatter: (value) => `$${(value / 1000).toFixed(2)}T`
        }
      ]
    }
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      {/* Header */}
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Bitcoin-M2 Money Supply Correlation Analysis
          </h3>
          <p className="mt-1 text-gray-500 text-theme-sm dark:text-gray-400">
            Statistical correlation between Bitcoin price movements and global M2 money supply
          </p>
        </div>
        
        {/* Period Selector */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Period:
          </label>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading M2 correlation data...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 mb-2">⚠️</div>
            <p className="text-gray-600">{error}</p>
            <button
              onClick={() => fetchCorrelationData(selectedPeriod)}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Content */}
      {!isLoading && !error && correlationData && (
        <>
          {/* Correlation Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Correlation Coefficient</div>
              <div
                className="text-lg font-bold"
                style={{ color: getCorrelationColor(correlationData.correlation_analysis?.correlation || null) }}
              >
                {correlationData.correlation_analysis?.correlation?.toFixed(3) || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">
                {getCorrelationDescription(correlationData.correlation_analysis?.correlation || null)}
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Data Points</div>
              <div className="text-lg font-bold text-gray-800 dark:text-white">
                {correlationData.summary.aligned_points}
              </div>
              <div className="text-sm text-gray-600">
                Aligned observations
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Latest M2 Supply</div>
              <div className="text-lg font-bold text-purple-600">
                ${(correlationData.summary.latest_m2_value! / 1000).toFixed(2)}T
              </div>
              <div className="text-sm text-gray-600">
                Current supply
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Latest BTC Price</div>
              <div className="text-lg font-bold text-orange-500">
                ${correlationData.summary.latest_btc_price?.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">
                Current price
              </div>
            </div>
          </div>

          {/* Chart */}
          <div className="max-w-full overflow-x-auto custom-scrollbar">
            <div className="min-w-[1000px] xl:min-w-full">
              <Chart
                options={chartOptions}
                series={chartSeries}
                type="line"
                height={400}
              />
            </div>
          </div>

          {/* Analysis Summary */}
          {correlationData.correlation_analysis && (
            <div className="mt-6 p-4 rounded-lg bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
              <h4 className="text-sm font-semibold text-purple-800 dark:text-purple-200 mb-2">
                Analysis Summary
              </h4>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                The correlation coefficient of <strong>{correlationData.correlation_analysis.correlation.toFixed(3)}</strong> indicates a{" "}
                <strong>{getCorrelationDescription(correlationData.correlation_analysis.correlation).toLowerCase()}</strong> relationship
                between Bitcoin prices and global M2 money supply over the selected {selectedPeriod}-day period.
                {correlationData.correlation_analysis.p_value && (
                  <> The statistical significance is <strong>{correlationData.correlation_analysis.significance}</strong> 
                  (p-value: {correlationData.correlation_analysis.p_value.toFixed(4)}).</>
                )}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default M2CorrelationChart;
